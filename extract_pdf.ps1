# Simple PDF text extraction attempt using PowerShell
param(
    [string]$PdfPath
)

try {
    # Try to read PDF as text (this might not work for all PDFs)
    $content = Get-Content -Path $PdfPath -Raw -Encoding UTF8
    
    # Look for readable text patterns
    if ($content -match "[\x20-\x7E]+") {
        Write-Host "Found some readable content:"
        # Extract printable ASCII characters
        $readableText = [regex]::Matches($content, "[\x20-\x7E]{10,}") | ForEach-Object { $_.Value }
        $readableText | Select-Object -First 20
    } else {
        Write-Host "No readable text found in standard encoding"
    }
    
    # Try to get file properties
    $file = Get-Item $PdfPath
    Write-Host "`nFile Information:"
    Write-Host "Name: $($file.Name)"
    Write-Host "Size: $($file.Length) bytes"
    Write-Host "Created: $($file.CreationTime)"
    Write-Host "Modified: $($file.LastWriteTime)"
    
} catch {
    Write-Host "Error reading PDF: $($_.Exception.Message)"
}
